import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import { writeFile, mkdir } from "fs/promises"
import { join } from "path"
import { v4 as uuidv4 } from "uuid"

// GET /api/tasks/[id]/attachments - Get task attachments
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: taskId } = params

    // Check if task exists and user has access
    const taskResult = await serverDb.sql`
      SELECT * FROM tasks WHERE id = ${taskId}
    `

    if (taskResult.length === 0) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 })
    }

    const task = taskResult[0]

    // Check access permissions
    const hasAccess = 
      ["admin", "manager"].includes(user.role) ||
      task.assigned_to === user.id ||
      task.assigned_by === user.id

    if (!hasAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Get attachments with user information
    const attachments = await serverDb.sql`
      SELECT 
        a.*,
        u.full_name as uploaded_by_name,
        u.email as uploaded_by_email
      FROM task_attachments a
      LEFT JOIN users u ON a.uploaded_by = u.id
      WHERE a.task_id = ${taskId}
      ORDER BY a.created_at DESC
    `

    return NextResponse.json({
      success: true,
      data: attachments
    })

  } catch (error) {
    console.error("Get task attachments error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/tasks/[id]/attachments - Upload attachment to task
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: taskId } = params

    // Check if task exists and user has access
    const taskResult = await serverDb.sql`
      SELECT * FROM tasks WHERE id = ${taskId}
    `

    if (taskResult.length === 0) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 })
    }

    const task = taskResult[0]

    // Check access permissions
    const hasAccess = 
      ["admin", "manager"].includes(user.role) ||
      task.assigned_to === user.id ||
      task.assigned_by === user.id

    if (!hasAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Parse form data
    const formData = await request.formData()
    const file = formData.get("file") as File

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 })
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return NextResponse.json({ error: "File too large. Maximum size is 10MB." }, { status: 400 })
    }

    // Validate file type (basic security check)
    const allowedTypes = [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'application/pdf', 'text/plain', 'text/csv',
      'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/zip', 'application/x-zip-compressed'
    ]

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ 
        error: "File type not allowed. Supported types: images, PDF, text, Word, Excel, ZIP" 
      }, { status: 400 })
    }

    // Generate unique filename
    const fileExtension = file.name.split('.').pop()
    const uniqueFilename = `${uuidv4()}.${fileExtension}`
    
    // Create upload directory if it doesn't exist
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'tasks', taskId)
    await mkdir(uploadDir, { recursive: true })

    // Save file
    const filePath = join(uploadDir, uniqueFilename)
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    await writeFile(filePath, buffer)

    // Store file info in database
    const relativePath = `/uploads/tasks/${taskId}/${uniqueFilename}`
    
    const attachmentResult = await serverDb.sql`
      INSERT INTO task_attachments (
        task_id, filename, original_filename, file_path, 
        file_size, mime_type, uploaded_by
      )
      VALUES (
        ${taskId}, ${uniqueFilename}, ${file.name}, ${relativePath},
        ${file.size}, ${file.type}, ${user.id}
      )
      RETURNING *
    `

    const newAttachment = attachmentResult[0]

    // Get the complete attachment data with user info
    const completeAttachmentResult = await serverDb.sql`
      SELECT 
        a.*,
        u.full_name as uploaded_by_name,
        u.email as uploaded_by_email
      FROM task_attachments a
      LEFT JOIN users u ON a.uploaded_by = u.id
      WHERE a.id = ${newAttachment.id}
    `

    return NextResponse.json({
      success: true,
      data: completeAttachmentResult[0],
      message: "File uploaded successfully"
    }, { status: 201 })

  } catch (error) {
    console.error("Upload attachment error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
