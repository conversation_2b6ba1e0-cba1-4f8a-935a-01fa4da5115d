// Admin Payroll Dashboard
// Phase 4: User Interface Development - Admin Dashboard

"use client"

import React, { useState, useEffect } from 'react'
import {
  Calculator,
  Users,
  DollarSign,
  TrendingUp,
  Calendar,
  FileText,
  AlertTriangle,
  CheckCircle,
  Clock,
  Download,
  Settings,
  Plus,
  Edit,
  Trash2,
  Search,
  Filter
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { CurrencyCard, CurrencyDisplay } from '@/components/ui/currency-display'
import { FiscalYearSelector, PayrollPeriodSelector } from '@/components/ui/fiscal-year-selector'
import { ComplianceDashboard } from '@/components/ui/compliance-dashboard'
import { EnhancedPayrollForm } from '@/components/payroll/enhanced-payroll-form'
import { fiscalYearManager } from '@/lib/fiscal-year-manager'
import { nprFormatter } from '@/lib/currency-formatter'
import { NepaliCalendar } from '@/lib/nepali-calendar'
import { toast } from '@/hooks/use-toast'

interface PayrollRecord {
  id: string
  employeeId: string
  employeeName: string
  position: string
  baseSalary: number
  overtime: number
  bonuses: number
  deductions: number
  netPay: number
  payPeriod: string
  status: "Draft" | "Pending" | "Processed"
  avatar?: string
}

interface PayrollDashboardData {
  overview: {
    totalEmployees: number
    activePayrolls: number
    pendingApprovals: number
    totalPayrollAmount: number
    lastProcessedDate: string
  }
  currentPeriod: {
    name: string
    startDate: string
    endDate: string
    workingDays: number
    processedEmployees: number
    pendingEmployees: number
    status: 'active' | 'processing' | 'completed'
  }
  recentActivity: {
    id: string
    type: 'processed' | 'approved' | 'paid' | 'error'
    employee: string
    amount: number
    timestamp: string
    status: string
  }[]
  monthlyTrends: {
    month: string
    totalPayroll: number
    employeeCount: number
    averagePay: number
  }[]
  complianceAlerts: {
    id: string
    type: 'warning' | 'error' | 'info'
    message: string
    employeeCount: number
    action: string
  }[]
}

const mockPayrollData: PayrollRecord[] = [
  {
    id: "1",
    employeeId: "EMP001",
    employeeName: "Ram Sharma",
    position: "Senior Developer",
    baseSalary: 65000,
    overtime: 8000,
    bonuses: 5000,
    deductions: 12000,
    netPay: 66000,
    payPeriod: "Kartik 2081",
    status: "Processed",
    avatar: "/images/avatar.png",
  },
  {
    id: "2",
    employeeId: "EMP002",
    employeeName: "Sita Poudel",
    position: "Project Manager",
    baseSalary: 75000,
    overtime: 6000,
    bonuses: 8000,
    deductions: 15000,
    netPay: 74000,
    payPeriod: "Kartik 2081",
    status: "Pending",
    avatar: "/images/avatar.png",
  },
  {
    id: "3",
    employeeId: "EMP003",
    employeeName: "Hari Thapa",
    position: "Designer",
    baseSalary: 55000,
    overtime: 4000,
    bonuses: 3000,
    deductions: 10000,
    netPay: 52000,
    payPeriod: "Kartik 2081",
    status: "Draft",
    avatar: "/images/avatar.png",
  },
]

export default function AdminPayrollPage() {
  const [dashboardData, setDashboardData] = useState<PayrollDashboardData | null>(null)
  const [payrollData, setPayrollData] = useState<PayrollRecord[]>(mockPayrollData)
  const [selectedFiscalYear, setSelectedFiscalYear] = useState(fiscalYearManager.getCurrentFiscalYear())
  const [selectedPeriod, setSelectedPeriod] = useState<any>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingRecord, setEditingRecord] = useState<PayrollRecord | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')


  useEffect(() => {
    loadDashboardData()
  }, [selectedFiscalYear, selectedPeriod])

  const loadDashboardData = async () => {
    setLoading(true)
    try {
      // Mock data - in production, this would come from API
      const mockData: PayrollDashboardData = {
        overview: {
          totalEmployees: 150,
          activePayrolls: 145,
          pendingApprovals: 12,
          totalPayrollAmount: 8500000,
          lastProcessedDate: new Date().toISOString()
        },
        currentPeriod: {
          name: 'Kartik 2081',
          startDate: '2024-10-17',
          endDate: '2024-11-15',
          workingDays: 26,
          processedEmployees: 138,
          pendingEmployees: 12,
          status: 'processing'
        },
        recentActivity: [
          {
            id: '1',
            type: 'processed',
            employee: 'Ram Sharma',
            amount: 65000,
            timestamp: new Date().toISOString(),
            status: 'completed'
          },
          {
            id: '2',
            type: 'approved',
            employee: 'Sita Poudel',
            amount: 58000,
            timestamp: new Date(Date.now() - 3600000).toISOString(),
            status: 'approved'
          },
          {
            id: '3',
            type: 'error',
            employee: 'Hari Thapa',
            amount: 0,
            timestamp: new Date(Date.now() - 7200000).toISOString(),
            status: 'error'
          }
        ],
        monthlyTrends: [
          { month: 'Shrawan', totalPayroll: 8200000, employeeCount: 148, averagePay: 55405 },
          { month: 'Bhadra', totalPayroll: 8350000, employeeCount: 149, averagePay: 56040 },
          { month: 'Ashwin', totalPayroll: 8400000, employeeCount: 150, averagePay: 56000 },
          { month: 'Kartik', totalPayroll: 8500000, employeeCount: 150, averagePay: 56667 }
        ],
        complianceAlerts: [
          {
            id: '1',
            type: 'warning',
            message: 'Overtime hours exceed daily limit',
            employeeCount: 5,
            action: 'Review overtime approvals'
          },
          {
            id: '2',
            type: 'error',
            message: 'Missing attendance data',
            employeeCount: 3,
            action: 'Update attendance records'
          }
        ]
      }

      setDashboardData(mockData)
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredData = payrollData.filter((record) => {
    const matchesSearch =
      record.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.employeeId.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || record.status === statusFilter
    return matchesSearch && matchesStatus
  })



  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'processing':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getActivityTypeColor = (type: string) => {
    switch (type) {
      case 'processed': return 'bg-blue-100 text-blue-800'
      case 'approved': return 'bg-green-100 text-green-800'
      case 'paid': return 'bg-purple-100 text-purple-800'
      case 'error': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const handleCreatePayroll = () => {
    setEditingRecord(null)
    setDialogOpen(true)
  }

  const handleEditPayroll = (record: PayrollRecord) => {
    setEditingRecord(record)
    setDialogOpen(true)
  }

  const handleSavePayroll = (payrollData: any) => {
    if (editingRecord) {
      // Update existing record
      setPayrollData((prev) =>
        prev.map((record) =>
          record.id === editingRecord.id
            ? {
                ...record,
                employeeId: payrollData.employeeId,
                employeeName: payrollData.employeeName,
                position: payrollData.position,
                baseSalary: payrollData.baseSalary,
                overtime: payrollData.overtime,
                bonuses: payrollData.bonuses,
                allowances: payrollData.allowances,
                deductions: payrollData.deductions,
                netPay: payrollData.netPay,
                payPeriod: payrollData.payPeriod,
                avatar: payrollData.avatar,
              }
            : record,
        ),
      )
      toast({
        title: "Payroll Updated",
        description: "Payroll record has been successfully updated",
      })
    } else {
      // Create new record
      const newRecord: PayrollRecord = {
        id: payrollData.id,
        employeeId: payrollData.employeeId,
        employeeName: payrollData.employeeName,
        position: payrollData.position,
        baseSalary: payrollData.baseSalary,
        overtime: payrollData.overtime,
        bonuses: payrollData.bonuses,
        deductions: payrollData.deductions,
        netPay: payrollData.netPay,
        payPeriod: payrollData.payPeriod,
        status: payrollData.status,
        avatar: payrollData.avatar,
      }
      setPayrollData((prev) => [...prev, newRecord])
      toast({
        title: "Payroll Created",
        description: "New payroll record has been successfully created",
      })
    }
  }

  const handleDeletePayroll = (id: string) => {
    setPayrollData((prev) => prev.filter((record) => record.id !== id))
    toast({
      title: "Payroll Deleted",
      description: "Payroll record has been successfully deleted",
    })
  }

  const handleStatusChange = (id: string, newStatus: "Draft" | "Pending" | "Processed") => {
    setPayrollData((prev) => prev.map((record) => (record.id === id ? { ...record, status: newStatus } : record)))
    toast({
      title: "Status Updated",
      description: `Payroll status changed to ${newStatus}`,
    })
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      Draft: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
      Pending: "bg-warning/10 text-warning-foreground border border-warning/20",
      Processed: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
    }
    return variants[status as keyof typeof variants] || variants.Draft
  }

  const stats = {
    totalPayroll: payrollData.reduce((sum, record) => sum + record.netPay, 0),
    processed: payrollData.filter((r) => r.status === "Processed").length,
    pending: payrollData.filter((r) => r.status === "Pending").length,
    draft: payrollData.filter((r) => r.status === "Draft").length,
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-3 sm:p-6 space-y-6">
      {/* Header Section */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
        <div>
          <h1 className="text-2xl lg:text-3xl font-bold">Payroll Dashboard</h1>
          <p className="text-muted-foreground">
            Manage payroll processing and monitor compliance
          </p>
        </div>
      </div>

      {/* Secondary Actions - Below main content on mobile */}
      <div className="flex flex-wrap items-center gap-2 justify-between border-b pb-4">
        <div className="flex flex-wrap items-center gap-2">
          <FiscalYearSelector
            value={selectedFiscalYear}
            onChange={setSelectedFiscalYear}
            showDetails={false}
            className="w-full sm:w-80"
          />
        </div>

        <Button size="sm" className="text-xs sm:text-sm">
          <Settings className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
          <span className="hidden sm:inline">Settings</span>
          <span className="sm:hidden">Config</span>
        </Button>
      </div>

      {/* Overview Cards */}
      {dashboardData && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <CurrencyCard
              title="Total Payroll"
              amount={dashboardData.overview.totalPayrollAmount}
              subtitle="Current month"
              trend={{ value: 5.2, period: "last month" }}
              icon={<DollarSign className="h-4 w-4" />}
              variant="success"
            />

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{dashboardData.overview.totalEmployees}</div>
                <p className="text-xs text-muted-foreground">
                  {dashboardData.overview.activePayrolls} active payrolls
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">
                  {dashboardData.overview.pendingApprovals}
                </div>
                <p className="text-xs text-muted-foreground">
                  Require approval
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Processing Status</CardTitle>
                <Calculator className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.round((dashboardData.currentPeriod.processedEmployees / dashboardData.overview.totalEmployees) * 100)}%
                </div>
                <Progress
                  value={(dashboardData.currentPeriod.processedEmployees / dashboardData.overview.totalEmployees) * 100}
                  className="mt-2"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  {dashboardData.currentPeriod.processedEmployees} of {dashboardData.overview.totalEmployees} processed
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Compliance Alerts */}
          {dashboardData.complianceAlerts.length > 0 && (
            <div className="space-y-4">
              <h2 className="text-xl font-semibold">Compliance Alerts</h2>
              <div className="grid gap-4">
                {dashboardData.complianceAlerts.map((alert) => (
                  <Alert key={alert.id} variant={alert.type === 'error' ? 'destructive' : 'default'}>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>
                      {alert.message}
                    </AlertTitle>
                    <AlertDescription>
                      Affects {alert.employeeCount} employee{alert.employeeCount !== 1 ? 's' : ''}.
                      Action required: {alert.action}
                    </AlertDescription>
                  </Alert>
                ))}
              </div>
            </div>
          )}

          {/* Main Content Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="processing">Processing</TabsTrigger>
              <TabsTrigger value="compliance">Compliance</TabsTrigger>
              <TabsTrigger value="records">Records</TabsTrigger>
            </TabsList>

            <TabsContent value="records" className="space-y-6">
              {/* Payroll Table */}
              <Card>
                <CardHeader>
                  <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                    <div>
                      <CardTitle>Payroll Records</CardTitle>
                      <p className="text-sm text-muted-foreground">Manage employee payroll and compensation details</p>
                    </div>
                    <Button onClick={handleCreatePayroll} size="sm" className="text-xs sm:text-sm">
                      <Plus className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="hidden sm:inline">Create Payroll</span>
                      <span className="sm:hidden">Create</span>
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4 mb-6">
                    <div className="relative flex-1">
                      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search employees..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger className="w-full sm:w-[180px]">
                        <Filter className="mr-2 h-4 w-4" />
                        <SelectValue placeholder="Filter by status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="Draft">Draft</SelectItem>
                        <SelectItem value="Pending">Pending</SelectItem>
                        <SelectItem value="Processed">Processed</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Responsive Table Container */}
                  <div className="overflow-x-auto border rounded-lg">

                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="min-w-[200px]">Employee</TableHead>
                          <TableHead className="hidden sm:table-cell">Position</TableHead>
                          <TableHead className="hidden lg:table-cell">Base Salary</TableHead>
                          <TableHead className="min-w-[120px]">Actions</TableHead>
                          <TableHead className="min-w-[100px]">Net Pay</TableHead>
                          <TableHead className="hidden md:table-cell">Status</TableHead>
                          <TableHead className="hidden xl:table-cell">Overtime</TableHead>
                          <TableHead className="hidden xl:table-cell">Bonuses</TableHead>
                          <TableHead className="hidden xl:table-cell">Deductions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredData.map((record) => (
                          <TableRow key={record.id} className="hover:bg-muted/50">
                            {/* Employee Info - Always visible */}
                            <TableCell className="min-w-[200px]">
                              <div className="flex items-center space-x-3">
                                <Avatar className="h-8 w-8 flex-shrink-0">
                                  <AvatarImage src={record.avatar || "/placeholder.svg"} />
                                  <AvatarFallback>{record.employeeName.charAt(0)}</AvatarFallback>
                                </Avatar>
                                <div className="min-w-0 flex-1">
                                  <div className="font-medium truncate">{record.employeeName}</div>
                                  <div className="text-sm text-muted-foreground truncate">{record.employeeId}</div>
                                  {/* Show additional info on mobile */}
                                  <div className="sm:hidden text-xs text-muted-foreground mt-1 space-y-1">
                                    <div className="lg:hidden">{record.position}</div>
                                    <div className="lg:hidden">Base: <CurrencyDisplay amount={record.baseSalary} size="xs" /></div>
                                    <div className="md:hidden">
                                      <Badge className="text-xs">
                                        {record.status}
                                      </Badge>
                                    </div>
                                    <div className="xl:hidden flex items-center space-x-2 text-xs">
                                      <span>OT: <CurrencyDisplay amount={record.overtime} size="xs" /></span>
                                      <span>•</span>
                                      <span>Bonus: <CurrencyDisplay amount={record.bonuses} size="xs" /></span>
                                      <span>•</span>
                                      <span>Ded: <CurrencyDisplay amount={record.deductions} size="xs" /></span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </TableCell>

                            {/* Position - Hidden on mobile */}
                            <TableCell className="hidden sm:table-cell">
                              <span className="text-sm">{record.position}</span>
                            </TableCell>

                            {/* Base Salary - Hidden on mobile and tablet */}
                            <TableCell className="hidden lg:table-cell">
                              <CurrencyDisplay amount={record.baseSalary} size="sm" />
                            </TableCell>

                            {/* Actions - Moved before net pay, always visible */}
                            <TableCell className="min-w-[120px]">
                              <div className="flex flex-col space-y-1">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleEditPayroll(record)}
                                  className="text-xs h-8 min-h-[44px] sm:min-h-[32px]"
                                >
                                  <Edit className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                                  <span className="hidden sm:inline">Edit</span>
                                  <span className="sm:hidden">Edit</span>
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleDeletePayroll(record.id)}
                                  className="text-red-600 hover:text-red-700 text-xs h-8 min-h-[44px] sm:min-h-[32px]"
                                >
                                  <Trash2 className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                                  <span className="hidden sm:inline">Delete</span>
                                  <span className="sm:hidden">Del</span>
                                </Button>
                              </div>
                            </TableCell>

                            {/* Net Pay - Always visible */}
                            <TableCell className="min-w-[100px] font-semibold">
                              <CurrencyDisplay amount={record.netPay} size="sm" />
                            </TableCell>

                            {/* Status - Hidden on mobile */}
                            <TableCell className="hidden md:table-cell">
                              <Select
                                value={record.status}
                                onValueChange={(value: "Draft" | "Pending" | "Processed") => handleStatusChange(record.id, value)}
                              >
                                <SelectTrigger className="w-[120px]">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="Draft">Draft</SelectItem>
                                  <SelectItem value="Pending">Pending</SelectItem>
                                  <SelectItem value="Processed">Processed</SelectItem>
                                </SelectContent>
                              </Select>
                            </TableCell>

                            {/* Overtime - Hidden on mobile and tablet */}
                            <TableCell className="hidden xl:table-cell">
                              <CurrencyDisplay amount={record.overtime} size="sm" />
                            </TableCell>

                            {/* Bonuses - Hidden on mobile and tablet */}
                            <TableCell className="hidden xl:table-cell">
                              <CurrencyDisplay amount={record.bonuses} size="sm" />
                            </TableCell>

                            {/* Deductions - Hidden on mobile and tablet */}
                            <TableCell className="hidden xl:table-cell">
                              <CurrencyDisplay amount={record.deductions} size="sm" />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Current Period Status */}
                <Card>
                  <CardHeader>
                    <CardTitle>Current Period Status</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{dashboardData.currentPeriod.name}</span>
                      <Badge variant={dashboardData.currentPeriod.status === 'completed' ? 'default' : 'secondary'}>
                        {dashboardData.currentPeriod.status}
                      </Badge>
                    </div>

                    <div className="text-sm text-muted-foreground">
                      {new Date(dashboardData.currentPeriod.startDate).toLocaleDateString()} - {new Date(dashboardData.currentPeriod.endDate).toLocaleDateString()}
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Working Days:</span>
                        <span className="font-medium">{dashboardData.currentPeriod.workingDays}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Processed:</span>
                        <span className="font-medium">{dashboardData.currentPeriod.processedEmployees}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Pending:</span>
                        <span className="font-medium text-yellow-600">{dashboardData.currentPeriod.pendingEmployees}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Recent Activity */}
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Activity</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {dashboardData.recentActivity.map((activity) => (
                        <div key={activity.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                          <div className="flex items-center gap-3">
                            {getStatusIcon(activity.status)}
                            <div>
                              <div className="font-medium">{activity.employee}</div>
                              <div className="text-sm text-muted-foreground">
                                {new Date(activity.timestamp).toLocaleTimeString()}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <Badge className={getActivityTypeColor(activity.type)}>
                              {activity.type}
                            </Badge>
                            {activity.amount > 0 && (
                              <div className="text-sm font-medium mt-1">
                                <CurrencyDisplay amount={activity.amount} size="sm" />
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Monthly Trends */}
              <Card>
                <CardHeader>
                  <CardTitle>Monthly Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    {dashboardData.monthlyTrends.map((trend, index) => (
                      <div key={index} className="text-center p-4 bg-muted rounded-lg">
                        <div className="font-medium text-sm">{trend.month}</div>
                        <div className="text-lg font-bold mt-1">
                          <CurrencyDisplay amount={trend.totalPayroll} size="sm" />
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {trend.employeeCount} employees
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Avg: <CurrencyDisplay amount={trend.averagePay} size="sm" />
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="processing" className="space-y-6">
              <PayrollPeriodSelector
                fiscalYear={selectedFiscalYear}
                value={selectedPeriod}
                onChange={setSelectedPeriod}
                periodType="monthly"
              />

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <Card className="lg:col-span-2">
                  <CardHeader>
                    <CardTitle>Payroll Processing Queue</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8 text-muted-foreground">
                      Select a payroll period to view processing queue
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button className="w-full">
                      <Calculator className="h-4 w-4 mr-2" />
                      Calculate Payroll
                    </Button>
                    <Button variant="outline" className="w-full">
                      <FileText className="h-4 w-4 mr-2" />
                      Generate Reports
                    </Button>
                    <Button variant="outline" className="w-full">
                      <Download className="h-4 w-4 mr-2" />
                      Export Data
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="compliance" className="space-y-6">
              <ComplianceDashboard />
            </TabsContent>
          </Tabs>
        </>
      )}

      {/* Enhanced Create/Edit Payroll Dialog */}
      <EnhancedPayrollForm
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        editingRecord={editingRecord}
        onSave={handleSavePayroll}
      />
    </div>
  )
}
