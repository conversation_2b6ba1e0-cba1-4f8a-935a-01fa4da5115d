// Utility functions for attendance management

export interface TimeCalculation {
  hours: number
  minutes: number
  totalHours: number
  formattedDuration: string
}

/**
 * Calculate the time difference between two timestamps
 */
export function calculateWorkingHours(checkInTime: string, checkOutTime: string): TimeCalculation {
  const checkIn = new Date(checkInTime)
  const checkOut = new Date(checkOutTime)
  
  const diffMs = checkOut.getTime() - checkIn.getTime()
  const totalMinutes = Math.floor(diffMs / (1000 * 60))
  const hours = Math.floor(totalMinutes / 60)
  const minutes = totalMinutes % 60
  const totalHours = Math.round((diffMs / (1000 * 60 * 60)) * 100) / 100

  return {
    hours,
    minutes,
    totalHours,
    formattedDuration: `${hours}h ${minutes}m`
  }
}

/**
 * Format time for display
 */
export function formatTime(timeString?: string): string {
  if (!timeString) return "-"
  return new Date(timeString).toLocaleTimeString([], { 
    hour: "2-digit", 
    minute: "2-digit",
    hour12: true 
  })
}

/**
 * Format time for input fields (24-hour format)
 */
export function formatTimeForInput(timeString: string): string {
  return new Date(timeString).toTimeString().slice(0, 5)
}

/**
 * Format date for display
 */
export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString([], {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

/**
 * Get current time in ISO format
 */
export function getCurrentISOTime(): string {
  return new Date().toISOString()
}

/**
 * Get today's date in YYYY-MM-DD format
 */
export function getTodayDate(): string {
  return new Date().toISOString().split('T')[0]
}

/**
 * Check if a time is considered late (after 9:15 AM)
 */
export function isLateCheckIn(checkInTime: string): boolean {
  const checkIn = new Date(checkInTime)
  const hour = checkIn.getHours()
  const minute = checkIn.getMinutes()
  
  // Consider late if after 9:15 AM
  return hour > 9 || (hour === 9 && minute > 15)
}

/**
 * Get attendance status based on check-in time
 */
export function getAttendanceStatus(checkInTime?: string, checkOutTime?: string): 'present' | 'late' | 'absent' {
  if (!checkInTime) return 'absent'
  if (isLateCheckIn(checkInTime)) return 'late'
  return 'present'
}

/**
 * Calculate expected work hours for a day (default 8 hours)
 */
export function getExpectedWorkHours(): number {
  return 8
}

/**
 * Check if work hours are sufficient
 */
export function isSufficientWorkHours(hoursWorked: number): boolean {
  return hoursWorked >= getExpectedWorkHours()
}

/**
 * Get time zone offset for proper time handling
 */
export function getTimezoneOffset(): number {
  return new Date().getTimezoneOffset()
}

/**
 * Convert local time to UTC for database storage
 */
export function localTimeToUTC(localTime: string, date: string): string {
  const localDateTime = new Date(`${date}T${localTime}`)
  return localDateTime.toISOString()
}

/**
 * Convert UTC time to local time for display
 */
export function utcToLocalTime(utcTime: string): string {
  return new Date(utcTime).toLocaleTimeString([], { 
    hour: "2-digit", 
    minute: "2-digit" 
  })
}

/**
 * Get work duration in real-time for currently checked-in users
 */
export function getCurrentWorkDuration(checkInTime: string): TimeCalculation {
  return calculateWorkingHours(checkInTime, getCurrentISOTime())
}

/**
 * Calculate total daily hours across multiple sessions
 */
export function calculateTotalDailyHours(attendanceEntries: Array<{
  check_in_time?: string;
  check_out_time?: string;
  hours_worked?: number;
}>): {
  totalHours: number;
  completedSessions: number;
  activeSessions: number;
  formattedTotal: string;
} {
  let totalHours = 0;
  let completedSessions = 0;
  let activeSessions = 0;

  attendanceEntries.forEach(entry => {
    if (entry.check_in_time && entry.check_out_time && entry.hours_worked) {
      // Completed session - use stored hours_worked
      totalHours += Number(entry.hours_worked);
      completedSessions++;
    } else if (entry.check_in_time && !entry.check_out_time) {
      // Active session - calculate current duration
      const currentDuration = getCurrentWorkDuration(entry.check_in_time);
      totalHours += currentDuration.hours;
      activeSessions++;
    }
  });

  return {
    totalHours: Math.round(totalHours * 100) / 100,
    completedSessions,
    activeSessions,
    formattedTotal: formatDuration(totalHours * 3600) // Convert hours to seconds for formatting
  };
}

/**
 * Handle incomplete sessions (check-in without check-out)
 */
export function handleIncompleteSession(checkInTime: string, currentTime?: string): {
  duration: TimeCalculation;
  isOvertime: boolean;
  suggestedAction: string;
} {
  const now = currentTime || getCurrentISOTime();
  const duration = calculateWorkingHours(checkInTime, now);

  // Consider overtime if session is longer than 8 hours
  const isOvertime = duration.hours > 8;

  let suggestedAction = 'Continue working';
  if (duration.hours > 12) {
    suggestedAction = 'Consider checking out - very long session detected';
  } else if (isOvertime) {
    suggestedAction = 'Overtime detected - please verify if intended';
  }

  return {
    duration,
    isOvertime,
    suggestedAction
  };
}

/**
 * Validate daily attendance limits
 */
export function validateDailyLimits(
  checkInsToday: number,
  checkOutsToday: number,
  action: 'check-in' | 'check-out'
): {
  isValid: boolean;
  message: string;
  remainingCheckIns: number;
  remainingCheckOuts: number;
} {
  const maxDaily = 5;
  const remainingCheckIns = Math.max(0, maxDaily - checkInsToday);
  const remainingCheckOuts = Math.max(0, maxDaily - checkOutsToday);

  if (action === 'check-in' && checkInsToday >= maxDaily) {
    return {
      isValid: false,
      message: `Maximum ${maxDaily} check-ins per day allowed. You have reached the limit.`,
      remainingCheckIns,
      remainingCheckOuts
    };
  }

  if (action === 'check-out' && checkOutsToday >= maxDaily) {
    return {
      isValid: false,
      message: `Maximum ${maxDaily} check-outs per day allowed. You have reached the limit.`,
      remainingCheckIns,
      remainingCheckOuts
    };
  }

  return {
    isValid: true,
    message: `${action === 'check-in' ? remainingCheckIns : remainingCheckOuts} ${action}s remaining today`,
    remainingCheckIns,
    remainingCheckOuts
  };
}

/**
 * Validate time input format
 */
export function isValidTimeFormat(time: string): boolean {
  const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/
  return timeRegex.test(time)
}

/**
 * Enhanced timezone handling
 */
export function getLocalTimezone(): string {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
}

/**
 * Convert UTC timestamp to local timezone for display
 */
export function formatTimeWithTimezone(utcTime: string, timezone?: string): string {
  const date = new Date(utcTime);
  const options: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZone: timezone || getLocalTimezone(),
    hour12: false
  };
  return date.toLocaleTimeString([], options);
}

/**
 * Get current date in user's timezone
 */
export function getCurrentDateInTimezone(timezone?: string): string {
  const now = new Date();
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    timeZone: timezone || getLocalTimezone()
  };
  return now.toLocaleDateString('en-CA', options); // Returns YYYY-MM-DD format
}

/**
 * Check if two timestamps are on the same day in a given timezone
 */
export function isSameDayInTimezone(time1: string, time2: string, timezone?: string): boolean {
  const date1 = new Date(time1);
  const date2 = new Date(time2);
  const tz = timezone || getLocalTimezone();

  const day1 = date1.toLocaleDateString('en-CA', { timeZone: tz });
  const day2 = date2.toLocaleDateString('en-CA', { timeZone: tz });

  return day1 === day2;
}

/**
 * Handle midnight transitions for attendance tracking
 */
export function handleMidnightTransition(checkInTime: string, currentTime?: string): {
  crossesMidnight: boolean;
  workingDays: number;
  adjustedHours: number;
  warning?: string;
} {
  const checkIn = new Date(checkInTime);
  const now = new Date(currentTime || getCurrentISOTime());

  const crossesMidnight = !isSameDayInTimezone(checkInTime, now.toISOString());
  const workingDays = Math.floor((now.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24)) + 1;

  // Calculate hours but cap at 24 hours per day for practical purposes
  const totalHours = (now.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
  const adjustedHours = Math.min(totalHours, 24);

  let warning;
  if (crossesMidnight) {
    warning = `Work session crosses midnight. Consider checking out and starting a new session for the next day.`;
  }

  return {
    crossesMidnight,
    workingDays,
    adjustedHours: Math.round(adjustedHours * 100) / 100,
    warning
  };
}

/**
 * Get attendance statistics summary
 */
export function getAttendanceStatsSummary(stats: {
  totalDays: number
  presentDays: number
  absentDays: number
  lateDays: number
  totalHoursWorked: number
}) {
  const attendanceRate = stats.totalDays > 0 ? (stats.presentDays / stats.totalDays) * 100 : 0
  const punctualityRate = stats.presentDays > 0 ? ((stats.presentDays - stats.lateDays) / stats.presentDays) * 100 : 0
  const avgHoursPerDay = stats.totalDays > 0 ? stats.totalHoursWorked / stats.totalDays : 0

  return {
    attendanceRate: Math.round(attendanceRate * 100) / 100,
    punctualityRate: Math.round(punctualityRate * 100) / 100,
    avgHoursPerDay: Math.round(avgHoursPerDay * 100) / 100
  }
}
