'use client'

import React, { useState, useEffect } from 'react'
import { Calendar, ChevronDown, Clock } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { fiscalYearManager, PayrollPeriod } from '@/lib/fiscal-year-manager'
import { NepaliCalendar } from '@/lib/nepali-calendar'
import { cn } from '@/lib/utils'

interface NepaliPayPeriodSelectorProps {
  value?: string
  onChange?: (period: PayrollPeriod) => void
  className?: string
  disabled?: boolean
  showFiscalYearSelector?: boolean
}

interface FiscalYearPeriod {
  fiscalYear: string
  displayName: string
  periods: PayrollPeriod[]
  isCurrent: boolean
}

export function NepaliPayPeriodSelector({
  value,
  onChange,
  className,
  disabled = false,
  showFiscalYearSelector = true
}: NepaliPayPeriodSelectorProps) {
  const [selectedFiscalYear, setSelectedFiscalYear] = useState<string>('')
  const [availableFiscalYears, setAvailableFiscalYears] = useState<FiscalYearPeriod[]>([])
  const [selectedPeriod, setSelectedPeriod] = useState<PayrollPeriod | null>(null)
  const [loading, setLoading] = useState(true)

  // Initialize fiscal years and periods
  useEffect(() => {
    const initializeFiscalYears = async () => {
      setLoading(true)
      try {
        const currentFY = fiscalYearManager.getCurrentFiscalYear()
        const fyList = fiscalYearManager.getAvailableFiscalYears(3) // Current + 2 previous years
        
        const fiscalYearPeriods: FiscalYearPeriod[] = []
        
        for (const fy of fyList) {
          const periods = await fiscalYearManager.createPayrollPeriods(fy)
          const monthlyPeriods = periods.filter(p => p.type === 'monthly')
          
          fiscalYearPeriods.push({
            fiscalYear: fy,
            displayName: `FY ${fy}`,
            periods: monthlyPeriods,
            isCurrent: fy === currentFY
          })
        }
        
        setAvailableFiscalYears(fiscalYearPeriods)
        
        // Set default fiscal year to current
        if (!selectedFiscalYear) {
          setSelectedFiscalYear(currentFY)
        }
      } catch (error) {
        console.error('Error initializing fiscal years:', error)
      } finally {
        setLoading(false)
      }
    }

    initializeFiscalYears()
  }, [])

  // Update selected period when fiscal year changes
  useEffect(() => {
    if (selectedFiscalYear && availableFiscalYears.length > 0) {
      const fyData = availableFiscalYears.find(fy => fy.fiscalYear === selectedFiscalYear)
      if (fyData && fyData.periods.length > 0) {
        // Auto-select current month if it's the current fiscal year
        if (fyData.isCurrent) {
          const currentPeriod = fiscalYearManager.getCurrentPayrollPeriod('monthly')
          if (currentPeriod && fyData.periods.find(p => p.id === currentPeriod.id)) {
            setSelectedPeriod(currentPeriod)
            onChange?.(currentPeriod)
            return
          }
        }
        
        // Otherwise select the first period
        const firstPeriod = fyData.periods[0]
        setSelectedPeriod(firstPeriod)
        onChange?.(firstPeriod)
      }
    }
  }, [selectedFiscalYear, availableFiscalYears])

  // Handle period selection
  const handlePeriodChange = (periodId: string) => {
    const fyData = availableFiscalYears.find(fy => fy.fiscalYear === selectedFiscalYear)
    if (fyData) {
      const period = fyData.periods.find(p => p.id === periodId)
      if (period) {
        setSelectedPeriod(period)
        onChange?.(period)
      }
    }
  }

  // Get current fiscal year data
  const currentFYData = availableFiscalYears.find(fy => fy.fiscalYear === selectedFiscalYear)

  // Format period display name with both Nepali and English dates
  const formatPeriodName = (period: PayrollPeriod) => {
    const nepaliMonth = NepaliCalendar.getBSMonthName(period.bsStartDate.month, 'en')
    const nepaliYear = period.bsStartDate.year
    const adStart = period.adStartDate.toLocaleDateString('en-US', { month: 'short' })
    const adEnd = period.adEndDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
    
    return {
      primary: `${nepaliMonth} ${nepaliYear}`,
      secondary: `${adStart} - ${adEnd}`
    }
  }

  if (loading) {
    return (
      <div className={cn("space-y-2", className)}>
        <Label>Pay Period</Label>
        <div className="h-10 bg-muted animate-pulse rounded-md" />
      </div>
    )
  }

  return (
    <div className={cn("space-y-4", className)}>
      {showFiscalYearSelector && (
        <div className="space-y-2">
          <Label>Fiscal Year</Label>
          <Select
            value={selectedFiscalYear}
            onValueChange={setSelectedFiscalYear}
            disabled={disabled}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select fiscal year" />
            </SelectTrigger>
            <SelectContent>
              {availableFiscalYears.map((fy) => (
                <SelectItem key={fy.fiscalYear} value={fy.fiscalYear}>
                  <div className="flex items-center gap-2">
                    <span>{fy.displayName}</span>
                    {fy.isCurrent && (
                      <Badge variant="secondary" className="text-xs">Current</Badge>
                    )}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      <div className="space-y-2">
        <Label>Pay Period</Label>
        <Select
          value={selectedPeriod?.id || ''}
          onValueChange={handlePeriodChange}
          disabled={disabled || !currentFYData}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select pay period">
              {selectedPeriod && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <div className="text-left">
                    <div className="font-medium">
                      {formatPeriodName(selectedPeriod).primary}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {formatPeriodName(selectedPeriod).secondary}
                    </div>
                  </div>
                </div>
              )}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {currentFYData?.periods.map((period) => {
              const formatted = formatPeriodName(period)
              const isCurrentPeriod = period.isActive
              
              return (
                <SelectItem key={period.id} value={period.id}>
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      <div>
                        <div className="font-medium">{formatted.primary}</div>
                        <div className="text-xs text-muted-foreground">
                          {formatted.secondary}
                        </div>
                      </div>
                    </div>
                    {isCurrentPeriod && (
                      <Badge variant="default" className="text-xs ml-2">
                        Current
                      </Badge>
                    )}
                  </div>
                </SelectItem>
              )
            })}
          </SelectContent>
        </Select>
      </div>

      {selectedPeriod && (
        <div className="bg-muted/50 p-3 rounded-lg">
          <div className="text-sm space-y-1">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Working Days:</span>
              <span className="font-medium">{selectedPeriod.workingDays}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Period Status:</span>
              <Badge variant={selectedPeriod.isClosed ? "secondary" : "default"} className="text-xs">
                {selectedPeriod.isClosed ? "Closed" : "Active"}
              </Badge>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
