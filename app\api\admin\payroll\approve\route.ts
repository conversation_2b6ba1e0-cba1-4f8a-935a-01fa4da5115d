// Payroll Approval API Endpoint
// Handles payroll approval workflows and pending approvals

import { NextRequest, NextResponse } from 'next/server'
import { AuthService } from '@/lib/auth-service'
import { db } from '@/lib/db'

// GET - List pending approvals
export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'pending_approvals';
    const userId = searchParams.get('userId');
    const status = searchParams.get('status') || 'pending';
    const limit = parseInt(searchParams.get('limit') || '50');

    if (action === 'pending_approvals') {
      // Get pending payroll approvals
      const pendingApprovals = await db.sql`
        SELECT 
          pa.id,
          pa.payroll_id,
          pa.approval_status,
          pa.approval_level,
          pa.comments,
          pa.created_at,
          p.user_id,
          p.pay_period_start,
          p.pay_period_end,
          p.gross_pay,
          p.net_pay,
          p.status as payroll_status,
          u.full_name as employee_name,
          u.employee_id,
          u.department,
          approver.full_name as approver_name
        FROM payroll_approvals pa
        JOIN payroll p ON pa.payroll_id = p.id
        JOIN users u ON p.user_id = u.id
        LEFT JOIN users approver ON pa.approver_id = approver.id
        WHERE pa.approval_status = ${status}
        ${userId ? db.sql`AND p.user_id = ${userId}` : db.sql``}
        ORDER BY pa.created_at DESC
        LIMIT ${limit}
      `;

      return NextResponse.json({
        success: true,
        data: {
          approvals: pendingApprovals,
          total: pendingApprovals.length,
          status: status
        },
        message: `Retrieved ${pendingApprovals.length} ${status} approvals`
      });

    } else if (action === 'approval_history') {
      // Get approval history for a specific payroll or user
      const approvalHistory = await db.sql`
        SELECT 
          pa.id,
          pa.payroll_id,
          pa.approval_status,
          pa.approval_date,
          pa.comments,
          pa.approval_level,
          pa.created_at,
          p.pay_period_start,
          p.pay_period_end,
          p.gross_pay,
          p.net_pay,
          u.full_name as employee_name,
          approver.full_name as approver_name
        FROM payroll_approvals pa
        JOIN payroll p ON pa.payroll_id = p.id
        JOIN users u ON p.user_id = u.id
        LEFT JOIN users approver ON pa.approver_id = approver.id
        ${userId ? db.sql`WHERE p.user_id = ${userId}` : db.sql``}
        ORDER BY pa.created_at DESC
        LIMIT ${limit}
      `;

      return NextResponse.json({
        success: true,
        data: {
          history: approvalHistory,
          total: approvalHistory.length
        },
        message: `Retrieved ${approvalHistory.length} approval records`
      });

    } else if (action === 'approval_summary') {
      // Get approval summary statistics
      const summary = await db.sql`
        SELECT 
          approval_status,
          COUNT(*) as count,
          SUM(p.net_pay) as total_amount
        FROM payroll_approvals pa
        JOIN payroll p ON pa.payroll_id = p.id
        WHERE pa.created_at >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY approval_status
      `;

      return NextResponse.json({
        success: true,
        data: {
          summary: summary,
          period: 'Last 30 days'
        },
        message: 'Approval summary retrieved successfully'
      });

    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action parameter'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in payroll approve GET:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

// POST - Approve or reject payroll records
export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const {
      action = 'approve_single',
      payrollIds = [],
      payrollId,
      approvalStatus = 'approved', // approved, rejected
      comments = '',
      approvalLevel = 1
    } = body;

    if (action === 'approve_single') {
      if (!payrollId) {
        return NextResponse.json({
          success: false,
          error: 'Payroll ID is required'
        }, { status: 400 });
      }

      // Check if payroll exists and is in correct status
      const payroll = await db.sql`
        SELECT id, status, user_id, net_pay 
        FROM payroll 
        WHERE id = ${payrollId}
      `;

      if (payroll.length === 0) {
        return NextResponse.json({
          success: false,
          error: 'Payroll record not found'
        }, { status: 404 });
      }

      // Create or update approval record
      const approval = await db.sql`
        INSERT INTO payroll_approvals (
          payroll_id, approver_id, approval_status, approval_date,
          comments, approval_level, is_final_approval
        ) VALUES (
          ${payrollId}, ${user.id}, ${approvalStatus}, NOW(),
          ${comments}, ${approvalLevel}, true
        )
        ON CONFLICT (payroll_id, approver_id, approval_level)
        DO UPDATE SET
          approval_status = ${approvalStatus},
          approval_date = NOW(),
          comments = ${comments},
          updated_at = NOW()
        RETURNING *
      `;

      // Update payroll status if approved
      if (approvalStatus === 'approved') {
        await db.sql`
          UPDATE payroll 
          SET 
            status = 'approved',
            updated_at = NOW()
          WHERE id = ${payrollId}
        `;
      }

      return NextResponse.json({
        success: true,
        data: {
          approval: approval[0],
          payrollId: payrollId,
          status: approvalStatus
        },
        message: `Payroll ${approvalStatus} successfully`
      });

    } else if (action === 'bulk_approve') {
      if (!payrollIds || payrollIds.length === 0) {
        return NextResponse.json({
          success: false,
          error: 'Payroll IDs are required for bulk approval'
        }, { status: 400 });
      }

      const results = [];
      let successCount = 0;
      let failureCount = 0;

      for (const payrollId of payrollIds) {
        try {
          // Create approval record
          const approval = await db.sql`
            INSERT INTO payroll_approvals (
              payroll_id, approver_id, approval_status, approval_date,
              comments, approval_level, is_final_approval
            ) VALUES (
              ${payrollId}, ${user.id}, ${approvalStatus}, NOW(),
              ${comments}, ${approvalLevel}, true
            )
            ON CONFLICT (payroll_id, approver_id, approval_level)
            DO UPDATE SET
              approval_status = ${approvalStatus},
              approval_date = NOW(),
              comments = ${comments},
              updated_at = NOW()
            RETURNING *
          `;

          // Update payroll status if approved
          if (approvalStatus === 'approved') {
            await db.sql`
              UPDATE payroll 
              SET 
                status = 'approved',
                updated_at = NOW()
              WHERE id = ${payrollId}
            `;
          }

          results.push({
            payrollId: payrollId,
            status: 'success',
            approval: approval[0]
          });
          successCount++;

        } catch (error) {
          results.push({
            payrollId: payrollId,
            status: 'failed',
            error: error.message
          });
          failureCount++;
        }
      }

      return NextResponse.json({
        success: true,
        data: {
          results: results,
          summary: {
            total: payrollIds.length,
            successful: successCount,
            failed: failureCount
          }
        },
        message: `Bulk approval completed: ${successCount} successful, ${failureCount} failed`
      });

    } else {
      return NextResponse.json({
        success: false,
        error: 'Invalid action parameter'
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in payroll approve POST:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}

// PUT - Update approval status or comments
export async function PUT(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value;

    if (!sessionToken) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    const user = await AuthService.verifySession(sessionToken);

    if (!user || !["admin", "hr_manager"].includes(user.role)) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions'
      }, { status: 403 });
    }

    const body = await request.json();
    const {
      approvalId,
      approvalStatus,
      comments
    } = body;

    if (!approvalId) {
      return NextResponse.json({
        success: false,
        error: 'Approval ID is required'
      }, { status: 400 });
    }

    // Update approval record
    const updated = await db.sql`
      UPDATE payroll_approvals 
      SET 
        approval_status = COALESCE(${approvalStatus}, approval_status),
        comments = COALESCE(${comments}, comments),
        approval_date = CASE 
          WHEN ${approvalStatus} IS NOT NULL THEN NOW() 
          ELSE approval_date 
        END,
        updated_at = NOW()
      WHERE id = ${approvalId}
      RETURNING *
    `;

    if (updated.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Approval record not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: updated[0],
      message: 'Approval updated successfully'
    });

  } catch (error) {
    console.error('Error in payroll approve PUT:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}
