import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import { z } from "zod"

// Validation schemas
const createTaskSchema = z.object({
  title: z.string().min(1, "Title is required").max(255, "Title too long"),
  description: z.string().optional(),
  assigned_to: z.string().uuid().optional(),
  priority: z.enum(["low", "medium", "high", "urgent"]).default("medium"),
  due_date: z.string().datetime().optional(),
  project_id: z.string().uuid().optional(),
  estimated_hours: z.number().positive().optional(),
})

const querySchema = z.object({
  status: z.enum(["todo", "in_progress", "completed", "cancelled"]).optional(),
  priority: z.enum(["low", "medium", "high", "urgent"]).optional(),
  assigned_to: z.string().uuid().optional(),
  project_id: z.string().uuid().optional(),
  search: z.string().optional(),
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().max(100).default(20),
})

// GET /api/tasks - List tasks with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url)
    const queryParams = Object.fromEntries(searchParams.entries())
    
    const validatedQuery = querySchema.parse(queryParams)
    const { status, priority, assigned_to, project_id, search, page, limit } = validatedQuery

    // Calculate offset for pagination
    const offset = (page - 1) * limit

    // Build the base query with user access control
    let whereConditions = []
    let queryParams_db = []
    let paramIndex = 1

    // Access control: users can only see tasks they're assigned to, created, or if they're admin/manager
    if (!["admin", "manager"].includes(user.role)) {
      whereConditions.push(`(assigned_to = $${paramIndex} OR assigned_by = $${paramIndex})`)
      queryParams_db.push(user.id)
      paramIndex++
    }

    // Apply filters
    if (status) {
      whereConditions.push(`status = $${paramIndex}`)
      queryParams_db.push(status)
      paramIndex++
    }

    if (priority) {
      whereConditions.push(`priority = $${paramIndex}`)
      queryParams_db.push(priority)
      paramIndex++
    }

    if (assigned_to) {
      whereConditions.push(`assigned_to = $${paramIndex}`)
      queryParams_db.push(assigned_to)
      paramIndex++
    }

    if (project_id) {
      whereConditions.push(`project_id = $${paramIndex}`)
      queryParams_db.push(project_id)
      paramIndex++
    }

    if (search) {
      whereConditions.push(`(title ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`)
      queryParams_db.push(`%${search}%`)
      paramIndex++
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM tasks t
      LEFT JOIN users assigned_user ON t.assigned_to = assigned_user.id
      LEFT JOIN users created_user ON t.assigned_by = created_user.id
      LEFT JOIN task_projects p ON t.project_id = p.id
      ${whereClause}
    `

    const countResult = await serverDb.sql.unsafe(countQuery, queryParams_db)
    const total = parseInt(countResult[0]?.total || "0")
    const totalPages = Math.ceil(total / limit)

    // Get tasks with related data
    const tasksQuery = `
      SELECT 
        t.*,
        assigned_user.full_name as assigned_to_name,
        assigned_user.email as assigned_to_email,
        created_user.full_name as created_by_name,
        created_user.email as created_by_email,
        p.name as project_name,
        p.color as project_color,
        (
          SELECT COUNT(*)
          FROM task_comments tc
          WHERE tc.task_id = t.id
        ) as comment_count,
        (
          SELECT COUNT(*)
          FROM task_attachments ta
          WHERE ta.task_id = t.id
        ) as attachment_count
      FROM tasks t
      LEFT JOIN users assigned_user ON t.assigned_to = assigned_user.id
      LEFT JOIN users created_user ON t.assigned_by = created_user.id
      LEFT JOIN task_projects p ON t.project_id = p.id
      ${whereClause}
      ORDER BY 
        CASE t.status 
          WHEN 'todo' THEN 1
          WHEN 'in_progress' THEN 2
          WHEN 'completed' THEN 3
          WHEN 'cancelled' THEN 4
        END,
        t.position ASC,
        t.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `

    queryParams_db.push(limit, offset)
    const tasks = await serverDb.sql.unsafe(tasksQuery, queryParams_db)

    return NextResponse.json({
      success: true,
      data: {
        tasks,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        }
      }
    })

  } catch (error) {
    console.error("Tasks API error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/tasks - Create a new task
export async function POST(request: NextRequest) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = createTaskSchema.parse(body)

    // Check if user can assign tasks to others (only admin/manager can assign to anyone)
    if (validatedData.assigned_to && validatedData.assigned_to !== user.id) {
      if (!["admin", "manager"].includes(user.role)) {
        return NextResponse.json(
          { error: "You can only assign tasks to yourself" },
          { status: 403 }
        )
      }
    }

    // If no assignee specified, assign to creator
    const assigned_to = validatedData.assigned_to || user.id

    // Get the next position for the task in the todo column
    const positionResult = await serverDb.sql`
      SELECT COALESCE(MAX(position), 0) + 1 as next_position
      FROM tasks
      WHERE status = 'todo'
    `
    const position = positionResult[0]?.next_position || 1

    // Create the task
    const taskResult = await serverDb.sql`
      INSERT INTO tasks (
        title, description, assigned_to, assigned_by, priority, 
        due_date, project_id, estimated_hours, position, status
      )
      VALUES (
        ${validatedData.title},
        ${validatedData.description || null},
        ${assigned_to},
        ${user.id},
        ${validatedData.priority},
        ${validatedData.due_date || null},
        ${validatedData.project_id || null},
        ${validatedData.estimated_hours || null},
        ${position},
        'todo'
      )
      RETURNING *
    `

    const newTask = taskResult[0]

    // Get the complete task data with related information
    const completeTaskResult = await serverDb.sql`
      SELECT 
        t.*,
        assigned_user.full_name as assigned_to_name,
        assigned_user.email as assigned_to_email,
        created_user.full_name as created_by_name,
        created_user.email as created_by_email,
        p.name as project_name,
        p.color as project_color
      FROM tasks t
      LEFT JOIN users assigned_user ON t.assigned_to = assigned_user.id
      LEFT JOIN users created_user ON t.assigned_by = created_user.id
      LEFT JOIN task_projects p ON t.project_id = p.id
      WHERE t.id = ${newTask.id}
    `

    return NextResponse.json({
      success: true,
      data: completeTaskResult[0],
      message: "Task created successfully"
    }, { status: 201 })

  } catch (error) {
    console.error("Create task error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid task data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
