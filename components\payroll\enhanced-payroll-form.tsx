'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { CurrencyDisplay } from '@/components/ui/currency-display'
import { EmployeeSelector, EmployeeOption } from './employee-selector'
import { NepaliPayPeriodSelector } from './nepali-pay-period-selector'
import { useEmployeePayrollProfile, calculateTotalAllowances, calculateTotalDeductions } from '@/hooks/use-employee-payroll'
import { PayrollPeriod } from '@/lib/fiscal-year-manager'
import { Loader2, User, Building, Calendar, DollarSign, Plus, Minus } from 'lucide-react'
import { toast } from 'sonner'

interface PayrollFormData {
  employeeId: string
  employeeName: string
  position: string
  baseSalary: string
  overtime: string
  bonuses: string
  deductions: string
  payPeriod: PayrollPeriod | null
  allowances: string
}

interface EnhancedPayrollFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  editingRecord?: any
  onSave: (data: any) => void
  title?: string
  description?: string
}

export function EnhancedPayrollForm({
  open,
  onOpenChange,
  editingRecord,
  onSave,
  title,
  description
}: EnhancedPayrollFormProps) {
  const [selectedEmployee, setSelectedEmployee] = useState<EmployeeOption | null>(null)
  const [selectedPeriod, setSelectedPeriod] = useState<PayrollPeriod | null>(null)
  const [formData, setFormData] = useState<PayrollFormData>({
    employeeId: '',
    employeeName: '',
    position: '',
    baseSalary: '',
    overtime: '',
    bonuses: '',
    deductions: '',
    payPeriod: null,
    allowances: ''
  })
  const [loading, setLoading] = useState(false)

  // Fetch employee payroll profile when employee is selected
  const { data: employeeProfile, isLoading: profileLoading, error: profileError } = useEmployeePayrollProfile(
    selectedEmployee?.id || null
  )

  // Debug logging
  useEffect(() => {
    console.log('Enhanced Payroll Form Debug:');
    console.log('- Selected Employee:', selectedEmployee);
    console.log('- Profile Loading:', profileLoading);
    console.log('- Profile Error:', profileError);
    console.log('- Employee Profile:', employeeProfile);
  }, [selectedEmployee, profileLoading, profileError, employeeProfile])

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (open) {
      if (editingRecord) {
        // Populate form with existing record data
        setFormData({
          employeeId: editingRecord.employeeId || '',
          employeeName: editingRecord.employeeName || '',
          position: editingRecord.position || '',
          baseSalary: editingRecord.baseSalary?.toString() || '',
          overtime: editingRecord.overtime?.toString() || '',
          bonuses: editingRecord.bonuses?.toString() || '',
          deductions: editingRecord.deductions?.toString() || '',
          payPeriod: editingRecord.payPeriod || null,
          allowances: editingRecord.allowances?.toString() || ''
        })
        // Note: In a real implementation, you'd need to find the employee by ID
        // setSelectedEmployee(findEmployeeById(editingRecord.employeeId))
      } else {
        // Reset form for new record
        setFormData({
          employeeId: '',
          employeeName: '',
          position: '',
          baseSalary: '',
          overtime: '',
          bonuses: '',
          deductions: '',
          payPeriod: null,
          allowances: ''
        })
        setSelectedEmployee(null)
        setSelectedPeriod(null)
      }
    }
  }, [open, editingRecord])

  // Auto-populate employee data when employee is selected
  useEffect(() => {
    console.log('Auto-populate useEffect triggered');
    console.log('- selectedEmployee:', selectedEmployee);
    console.log('- employeeProfile:', employeeProfile);
    console.log('- employeeProfile?.success:', employeeProfile?.success);

    if (selectedEmployee && employeeProfile?.success) {
      console.log('Conditions met, auto-populating data...');
      const profile = employeeProfile.data

      console.log('Profile data:', profile);

      // Calculate total allowances
      const totalAllowances = calculateTotalAllowances(
        profile.allowances || [],
        profile.salary || 0
      )

      // Calculate total deductions (excluding taxes which will be calculated separately)
      const totalDeductions = calculateTotalDeductions(
        profile.deductions || [],
        profile.salary || 0,
        (profile.salary || 0) + totalAllowances
      )

      console.log('Calculated values:');
      console.log('- Total Allowances:', totalAllowances);
      console.log('- Total Deductions:', totalDeductions);

      setFormData(prev => ({
        ...prev,
        employeeId: profile.id,
        employeeName: profile.full_name,
        position: profile.position || '',
        baseSalary: profile.salary?.toString() || '',
        allowances: totalAllowances.toString(),
        deductions: totalDeductions.toString()
      }))

      toast.success(`Employee data loaded for ${profile.full_name}`)
    } else {
      console.log('Conditions not met for auto-population');
    }
  }, [selectedEmployee, employeeProfile])

  // Handle employee selection
  const handleEmployeeSelect = (employee: EmployeeOption | null) => {
    setSelectedEmployee(employee)
    if (!employee) {
      // Clear form when no employee selected
      setFormData(prev => ({
        ...prev,
        employeeId: '',
        employeeName: '',
        position: '',
        baseSalary: '',
        allowances: '',
        deductions: ''
      }))
    }
  }

  // Handle pay period selection
  const handlePeriodSelect = (period: PayrollPeriod) => {
    setSelectedPeriod(period)
    setFormData(prev => ({
      ...prev,
      payPeriod: period
    }))
  }

  // Calculate net pay
  const calculateNetPay = () => {
    const baseSalary = parseFloat(formData.baseSalary) || 0
    const overtime = parseFloat(formData.overtime) || 0
    const bonuses = parseFloat(formData.bonuses) || 0
    const allowances = parseFloat(formData.allowances) || 0
    const deductions = parseFloat(formData.deductions) || 0
    
    const grossPay = baseSalary + overtime + bonuses + allowances
    return Math.max(0, grossPay - deductions)
  }

  // Handle form submission
  const handleSave = () => {
    if (!selectedEmployee) {
      toast.error('Please select an employee')
      return
    }

    if (!selectedPeriod) {
      toast.error('Please select a pay period')
      return
    }

    const netPay = calculateNetPay()
    
    const payrollData = {
      id: editingRecord?.id || Date.now().toString(),
      employeeId: formData.employeeId,
      employeeName: formData.employeeName,
      position: formData.position,
      baseSalary: parseFloat(formData.baseSalary) || 0,
      overtime: parseFloat(formData.overtime) || 0,
      bonuses: parseFloat(formData.bonuses) || 0,
      allowances: parseFloat(formData.allowances) || 0,
      deductions: parseFloat(formData.deductions) || 0,
      netPay,
      payPeriod: selectedPeriod.name,
      payPeriodData: selectedPeriod,
      status: editingRecord?.status || 'Draft',
      avatar: selectedEmployee.profile_picture || '/images/avatar.png'
    }

    onSave(payrollData)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {title || (editingRecord ? "Edit Payroll" : "Create New Payroll")}
          </DialogTitle>
          <DialogDescription>
            {description || (editingRecord 
              ? "Update payroll information for the employee" 
              : "Enter payroll details for the employee"
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Employee Selection */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <EmployeeSelector
              value={selectedEmployee?.id}
              onSelect={handleEmployeeSelect}
              disabled={!!editingRecord}
              className="lg:col-span-1"
            />
            
            <NepaliPayPeriodSelector
              value={selectedPeriod?.id}
              onChange={handlePeriodSelect}
              showFiscalYearSelector={false}
              className="lg:col-span-1"
            />
          </div>

          {/* Employee Info Display */}
          {selectedEmployee && employeeProfile?.success && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Employee Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <Label className="text-muted-foreground">Department</Label>
                    <div className="flex items-center gap-1 mt-1">
                      <Building className="h-4 w-4" />
                      {employeeProfile.data.department || 'Not specified'}
                    </div>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">Employee Type</Label>
                    <div className="mt-1">
                      <Badge variant="outline">
                        {employeeProfile.data.employee_type?.replace('_', ' ') || 'Not specified'}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <Label className="text-muted-foreground">Employment Status</Label>
                    <div className="mt-1">
                      <Badge variant={employeeProfile.data.employment_status === 'active' ? 'default' : 'secondary'}>
                        {employeeProfile.data.employment_status || 'Not specified'}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Salary and Compensation */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="position">Position</Label>
              <Input
                id="position"
                value={formData.position}
                onChange={(e) => setFormData(prev => ({ ...prev, position: e.target.value }))}
                placeholder="Enter position"
                disabled={profileLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="baseSalary">Base Salary (NPR)</Label>
              <Input
                id="baseSalary"
                type="number"
                value={formData.baseSalary}
                onChange={(e) => setFormData(prev => ({ ...prev, baseSalary: e.target.value }))}
                placeholder="0"
                disabled={profileLoading}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="allowances">Allowances (NPR)</Label>
              <Input
                id="allowances"
                type="number"
                value={formData.allowances}
                onChange={(e) => setFormData(prev => ({ ...prev, allowances: e.target.value }))}
                placeholder="0"
                disabled={profileLoading}
              />
              {profileLoading && (
                <div className="text-xs text-muted-foreground">
                  <Loader2 className="h-3 w-3 animate-spin inline mr-1" />
                  Loading configured allowances...
                </div>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="overtime">Overtime (NPR)</Label>
              <Input
                id="overtime"
                type="number"
                value={formData.overtime}
                onChange={(e) => setFormData(prev => ({ ...prev, overtime: e.target.value }))}
                placeholder="0"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="bonuses">Bonuses (NPR)</Label>
              <Input
                id="bonuses"
                type="number"
                value={formData.bonuses}
                onChange={(e) => setFormData(prev => ({ ...prev, bonuses: e.target.value }))}
                placeholder="0"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="deductions">Deductions (NPR)</Label>
              <Input
                id="deductions"
                type="number"
                value={formData.deductions}
                onChange={(e) => setFormData(prev => ({ ...prev, deductions: e.target.value }))}
                placeholder="0"
                disabled={profileLoading}
              />
              {profileLoading && (
                <div className="text-xs text-muted-foreground">
                  <Loader2 className="h-3 w-3 animate-spin inline mr-1" />
                  Loading configured deductions...
                </div>
              )}
            </div>
          </div>

          {/* Net Pay Calculation */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-center">
                <span className="font-medium">Net Pay:</span>
                <span className="text-2xl font-bold text-green-600">
                  <CurrencyDisplay amount={calculateNetPay()} />
                </span>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleSave}
            disabled={!selectedEmployee || !selectedPeriod || loading}
          >
            {loading && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
            {editingRecord ? "Update Payroll" : "Create Payroll"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
