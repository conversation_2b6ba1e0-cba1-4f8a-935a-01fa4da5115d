import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { toast } from "@/hooks/use-toast"

// Types
interface Task {
  id: string
  title: string
  description?: string
  status: "todo" | "in_progress" | "completed" | "cancelled"
  priority: "low" | "medium" | "high" | "urgent"
  assigned_to?: string
  assigned_by: string
  due_date?: string
  project_id?: string
  estimated_hours?: number
  actual_hours?: number
  position?: number
  created_at: string
  updated_at: string
  // Extended fields from joins
  assigned_to_name?: string
  assigned_to_email?: string
  created_by_name?: string
  created_by_email?: string
  project_name?: string
  project_color?: string
  comment_count?: number
  attachment_count?: number
  total_time_minutes?: number
}

interface TaskFilters {
  status?: string
  priority?: string
  assigned_to?: string
  project_id?: string
  search?: string
  page?: number
  limit?: number
}

interface CreateTaskData {
  title: string
  description?: string
  assigned_to?: string
  priority?: "low" | "medium" | "high" | "urgent"
  due_date?: string
  project_id?: string
  estimated_hours?: number
}

interface UpdateTaskData extends Partial<CreateTaskData> {
  status?: "todo" | "in_progress" | "completed" | "cancelled"
}

// API functions
const taskApi = {
  async getTasks(filters: TaskFilters = {}) {
    const params = new URLSearchParams()
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        params.append(key, value.toString())
      }
    })

    const response = await fetch(`/api/tasks?${params.toString()}`, {
      credentials: "include",
    })

    if (!response.ok) {
      throw new Error("Failed to fetch tasks")
    }

    return response.json()
  },

  async getTask(id: string) {
    const response = await fetch(`/api/tasks/${id}`, {
      credentials: "include",
    })

    if (!response.ok) {
      throw new Error("Failed to fetch task")
    }

    return response.json()
  },

  async createTask(data: CreateTaskData) {
    const response = await fetch("/api/tasks", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to create task")
    }

    return response.json()
  },

  async updateTask(id: string, data: UpdateTaskData) {
    const response = await fetch(`/api/tasks/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to update task")
    }

    return response.json()
  },

  async updateTaskStatus(id: string, status: string, position?: number) {
    const response = await fetch(`/api/tasks/${id}/status`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
      body: JSON.stringify({ status, position }),
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to update task status")
    }

    return response.json()
  },

  async deleteTask(id: string) {
    const response = await fetch(`/api/tasks/${id}`, {
      method: "DELETE",
      credentials: "include",
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || "Failed to delete task")
    }

    return response.json()
  },
}

// React Query hooks
export function useTasks(filters: TaskFilters = {}, options: { realTime?: boolean } = {}) {
  return useQuery({
    queryKey: ["tasks", filters],
    queryFn: () => taskApi.getTasks(filters),
    staleTime: options.realTime ? 0 : 30 * 1000, // No stale time for real-time
    refetchInterval: options.realTime ? 5 * 1000 : false, // Poll every 5 seconds for real-time
  })
}

export function useTask(id: string) {
  return useQuery({
    queryKey: ["task", id],
    queryFn: () => taskApi.getTask(id),
    enabled: !!id,
  })
}

export function useCreateTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: taskApi.createTask,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["tasks"] })
      toast({
        title: "Success",
        description: "Task created successfully",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
  })
}

export function useUpdateTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateTaskData }) =>
      taskApi.updateTask(id, data),
    onSuccess: (response, { id }) => {
      queryClient.invalidateQueries({ queryKey: ["tasks"] })
      queryClient.invalidateQueries({ queryKey: ["task", id] })
      toast({
        title: "Success",
        description: "Task updated successfully",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
  })
}

export function useUpdateTaskStatus() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, status, position }: { id: string; status: string; position?: number }) =>
      taskApi.updateTaskStatus(id, status, position),
    onMutate: async ({ id, status }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["tasks"] })

      // Snapshot the previous value
      const previousTasks = queryClient.getQueryData(["tasks"])

      // Optimistically update the cache
      queryClient.setQueryData(["tasks"], (old: any) => {
        if (!old?.data?.tasks) return old

        return {
          ...old,
          data: {
            ...old.data,
            tasks: old.data.tasks.map((task: Task) =>
              task.id === id ? { ...task, status } : task
            ),
          },
        }
      })

      return { previousTasks }
    },
    onError: (error: Error, variables, context) => {
      // Rollback on error
      if (context?.previousTasks) {
        queryClient.setQueryData(["tasks"], context.previousTasks)
      }
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["tasks"] })
    },
  })
}

export function useDeleteTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: taskApi.deleteTask,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["tasks"] })
      toast({
        title: "Success",
        description: "Task deleted successfully",
      })
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
    },
  })
}
