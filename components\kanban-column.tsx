"use client"

import type React from "react"

interface Task {
  id: string
  title: string
  description: string
  status: string
  userId: string
  priority?: string
}

interface KanbanColumnProps {
  title: string
  tasks?: Task[]
  onEditTask?: (task: Task) => void
  onDeleteTask?: (taskId: string) => void
  onUpdateTaskStatus?: (taskId: string, newStatus: string) => void
  isAdmin?: boolean
  currentUserId?: string
  columnId: string
  isUpdating?: boolean
}

export function KanbanColumn({
  title,
  tasks = [],
  onEditTask = () => {},
  onDeleteTask = () => {},
  onUpdateTaskStatus = () => {},
  isAdmin = false,
  currentUserId = "",
  columnId,
  isUpdating = false,
}: KanbanColumnProps) {
  // Ensure tasks is an array
  const tasksArray = Array.isArray(tasks) ? tasks : []

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    const taskId = e.dataTransfer.getData("taskId")
    if (taskId) {
      onUpdateTaskStatus(taskId, columnId)
    }
  }

  return (
    <div
      className={`flex flex-col h-full bg-gray-100/80 dark:bg-gray-800/80 rounded-lg p-3 min-h-[200px] ${
        isUpdating ? "opacity-50 pointer-events-none" : ""
      }`}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-medium text-sm text-gray-700 dark:text-gray-300">{title}</h3>
        <span className="bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs px-2 py-1 rounded-full">
          {tasksArray.length}
        </span>
      </div>
      <div className="space-y-2 overflow-y-auto">
        {tasksArray.length === 0 ? (
          <div className="text-center py-4 text-sm text-gray-500 dark:text-gray-400">No tasks</div>
        ) : (
          tasksArray.map((task) => (
            <TaskCard
              key={task.id}
              task={task}
              onEdit={() => onEditTask(task)}
              onDelete={() => onDeleteTask(task.id)}
              isAdmin={isAdmin}
              isOwnedByCurrentUser={task.assigned_to === currentUserId}
            />
          ))
        )}
      </div>
    </div>
  )
}

// Simple TaskCard component to avoid import issues
function TaskCard({ task, onEdit, onDelete, isAdmin, isOwnedByCurrentUser }) {
  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.setData("taskId", task.id)
  }

  // Determine if the current user can edit/delete this task
  const canModify = isAdmin || isOwnedByCurrentUser

  // Priority colors
  const priorityColors = {
    high: "border-red-500",
    medium: "border-orange-500",
    low: "border-blue-500",
  }

  const priorityColor = task.priority ? priorityColors[task.priority as keyof typeof priorityColors] : "border-gray-300"

  return (
    <div
      className={`bg-white dark:bg-gray-700 p-3 rounded-md shadow-sm cursor-grab border-l-4 ${priorityColor}`}
      draggable
      onDragStart={handleDragStart}
    >
      <div className="flex justify-between items-start">
        <h4 className="font-medium text-sm mb-1 dark:text-white">{task.title}</h4>
        {task.priority && (
          <span
            className={`text-xs px-2 py-0.5 rounded-full ${
              task.priority === "high"
                ? "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300"
                : task.priority === "medium"
                  ? "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300"
                  : "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300"
            }`}
          >
            {task.priority}
          </span>
        )}
      </div>
      <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">{task.description}</p>
      {canModify && (
        <div className="flex justify-end gap-2 mt-2">
          <button
            onClick={(e) => {
              e.stopPropagation()
              onEdit()
            }}
            className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-500"
          >
            Edit
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation()
              onDelete()
            }}
            className="text-xs px-2 py-1 bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-300 rounded hover:bg-red-200 dark:hover:bg-red-800/30"
          >
            Delete
          </button>
        </div>
      )}
    </div>
  )
}
