import { NextRequest, NextResponse } from "next/server"
import { AuthService } from "@/lib/auth-utils"
import { serverDb } from "@/lib/server-db"
import { z } from "zod"

// Validation schema for task updates
const updateTaskSchema = z.object({
  title: z.string().min(1, "Title is required").max(255, "Title too long").optional(),
  description: z.string().optional(),
  assigned_to: z.string().uuid().optional(),
  priority: z.enum(["low", "medium", "high", "urgent"]).optional(),
  status: z.enum(["todo", "in_progress", "completed", "cancelled"]).optional(),
  due_date: z.string().datetime().optional(),
  project_id: z.string().uuid().optional(),
  estimated_hours: z.number().positive().optional(),
})

// GET /api/tasks/[id] - Get specific task details
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id } = params

    // Get task with related data
    const taskResult = await serverDb.sql`
      SELECT 
        t.*,
        assigned_user.full_name as assigned_to_name,
        assigned_user.email as assigned_to_email,
        created_user.full_name as created_by_name,
        created_user.email as created_by_email,
        p.name as project_name,
        p.color as project_color,
        (
          SELECT COUNT(*)
          FROM task_comments tc
          WHERE tc.task_id = t.id
        ) as comment_count,
        (
          SELECT COUNT(*)
          FROM task_attachments ta
          WHERE ta.task_id = t.id
        ) as attachment_count,
        (
          SELECT COALESCE(SUM(duration_minutes), 0)
          FROM task_time_logs ttl
          WHERE ttl.task_id = t.id AND ttl.end_time IS NOT NULL
        ) as total_time_minutes
      FROM tasks t
      LEFT JOIN users assigned_user ON t.assigned_to = assigned_user.id
      LEFT JOIN users created_user ON t.assigned_by = created_user.id
      LEFT JOIN task_projects p ON t.project_id = p.id
      WHERE t.id = ${id}
    `

    if (taskResult.length === 0) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 })
    }

    const task = taskResult[0]

    // Check access permissions
    const hasAccess = 
      ["admin", "manager"].includes(user.role) ||
      task.assigned_to === user.id ||
      task.assigned_by === user.id

    if (!hasAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    return NextResponse.json({
      success: true,
      data: task
    })

  } catch (error) {
    console.error("Get task error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT /api/tasks/[id] - Update task
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id } = params

    // Check if task exists and user has permission
    const existingTaskResult = await serverDb.sql`
      SELECT * FROM tasks WHERE id = ${id}
    `

    if (existingTaskResult.length === 0) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 })
    }

    const existingTask = existingTaskResult[0]

    // Check permissions
    const canEdit = 
      ["admin", "manager"].includes(user.role) ||
      existingTask.assigned_to === user.id ||
      existingTask.assigned_by === user.id

    if (!canEdit) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = updateTaskSchema.parse(body)

    // Check if user can assign tasks to others
    if (validatedData.assigned_to && validatedData.assigned_to !== user.id) {
      if (!["admin", "manager"].includes(user.role)) {
        return NextResponse.json(
          { error: "You can only assign tasks to yourself" },
          { status: 403 }
        )
      }
    }

    // Build update query dynamically
    const updateFields = []
    const updateValues = []
    let paramIndex = 1

    Object.entries(validatedData).forEach(([key, value]) => {
      if (value !== undefined) {
        updateFields.push(`${key} = $${paramIndex}`)
        updateValues.push(value)
        paramIndex++
      }
    })

    if (updateFields.length === 0) {
      return NextResponse.json({ error: "No fields to update" }, { status: 400 })
    }

    // Add updated_at timestamp
    updateFields.push(`updated_at = NOW()`)

    // Update the task
    const updateQuery = `
      UPDATE tasks 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `
    updateValues.push(id)

    const updatedTaskResult = await serverDb.sql.unsafe(updateQuery, updateValues)

    // Get the complete updated task data
    const completeTaskResult = await serverDb.sql`
      SELECT 
        t.*,
        assigned_user.full_name as assigned_to_name,
        assigned_user.email as assigned_to_email,
        created_user.full_name as created_by_name,
        created_user.email as created_by_email,
        p.name as project_name,
        p.color as project_color
      FROM tasks t
      LEFT JOIN users assigned_user ON t.assigned_to = assigned_user.id
      LEFT JOIN users created_user ON t.assigned_by = created_user.id
      LEFT JOIN task_projects p ON t.project_id = p.id
      WHERE t.id = ${id}
    `

    return NextResponse.json({
      success: true,
      data: completeTaskResult[0],
      message: "Task updated successfully"
    })

  } catch (error) {
    console.error("Update task error:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid task data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// DELETE /api/tasks/[id] - Delete task
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const sessionToken = request.cookies.get("session-token")?.value

    if (!sessionToken) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await AuthService.verifySession(sessionToken)

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id } = params

    // Check if task exists and user has permission
    const existingTaskResult = await serverDb.sql`
      SELECT * FROM tasks WHERE id = ${id}
    `

    if (existingTaskResult.length === 0) {
      return NextResponse.json({ error: "Task not found" }, { status: 404 })
    }

    const existingTask = existingTaskResult[0]

    // Check permissions (only admin, manager, or task creator can delete)
    const canDelete = 
      ["admin", "manager"].includes(user.role) ||
      existingTask.assigned_by === user.id

    if (!canDelete) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Delete the task (cascade will handle related records)
    await serverDb.sql`
      DELETE FROM tasks WHERE id = ${id}
    `

    return NextResponse.json({
      success: true,
      message: "Task deleted successfully"
    })

  } catch (error) {
    console.error("Delete task error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
