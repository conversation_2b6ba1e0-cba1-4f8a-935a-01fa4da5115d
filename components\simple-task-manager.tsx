"use client"

import React, { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { useTasks, useUpdateTaskStatus, useDeleteTask } from "@/hooks/use-tasks"
import { formatEmployeeDisplayName } from "@/hooks/use-employees"
import { 
  Edit, 
  Trash2, 
  Search, 
  Filter, 
  Calendar,
  User,
  Flag,
  CheckCircle2,
  Circle,
  Clock,
  AlertCircle
} from "lucide-react"
import { format } from "date-fns"

interface Task {
  id: string
  title: string
  description?: string
  status: "todo" | "in_progress" | "completed" | "cancelled"
  priority: "low" | "medium" | "high" | "urgent"
  assigned_to?: string
  assigned_by: string
  due_date?: string
  project_id?: string
  created_at: string
  updated_at: string
  // Extended fields from joins
  assigned_to_name?: string
  assigned_to_email?: string
  project_name?: string
  project_color?: string
  comment_count?: number
  attachment_count?: number
}

interface SimpleTaskManagerProps {
  onEditTask?: (task: Task) => void
  onDeleteTask?: (taskId: string) => void
  isAdmin?: boolean
  currentUserId?: string
  filters?: {
    status?: string
    priority?: string
    assigned_to?: string
    project_id?: string
    search?: string
  }
  realTimeUpdates?: boolean
}

export function SimpleTaskManager({
  onEditTask = () => {},
  onDeleteTask = () => {},
  isAdmin = false,
  currentUserId = "",
  filters = {},
  realTimeUpdates = true,
}: SimpleTaskManagerProps) {
  const [localSearch, setLocalSearch] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [priorityFilter, setPriorityFilter] = useState("all")

  // Fetch tasks using React Query with optional real-time updates
  const { data: tasksResponse, isLoading, error } = useTasks(filters, { realTime: realTimeUpdates })
  const updateTaskStatusMutation = useUpdateTaskStatus()
  const deleteTaskMutation = useDeleteTask()

  // Get tasks from response with defensive programming
  const tasksArray = React.useMemo(() => {
    // Handle different possible response structures
    if (!tasksResponse) return []

    // If tasksResponse is already an array (direct response)
    if (Array.isArray(tasksResponse)) return tasksResponse

    // If tasksResponse has data.tasks structure (API wrapper)
    if (tasksResponse.data && Array.isArray(tasksResponse.data.tasks)) {
      return tasksResponse.data.tasks
    }

    // If tasksResponse has tasks directly
    if (Array.isArray(tasksResponse.tasks)) {
      return tasksResponse.tasks
    }

    // If tasksResponse.data is an array
    if (Array.isArray(tasksResponse.data)) {
      return tasksResponse.data
    }

    // Fallback to empty array
    console.warn('Unexpected tasks response structure:', tasksResponse)
    return []
  }, [tasksResponse])

  // Filter tasks based on local filters with additional safety check
  const filteredTasks = React.useMemo(() => {
    if (!Array.isArray(tasksArray)) {
      console.error('tasksArray is not an array:', tasksArray)
      return []
    }

    return tasksArray.filter((task: Task) => {
      const matchesSearch = !localSearch ||
        task.title.toLowerCase().includes(localSearch.toLowerCase()) ||
        task.description?.toLowerCase().includes(localSearch.toLowerCase()) ||
        task.assigned_to_name?.toLowerCase().includes(localSearch.toLowerCase())

      const matchesStatus = statusFilter === "all" || task.status === statusFilter
      const matchesPriority = priorityFilter === "all" || task.priority === priorityFilter

      return matchesSearch && matchesStatus && matchesPriority
    })
  }, [tasksArray, localSearch, statusFilter, priorityFilter])

  // Handle task status toggle
  const handleToggleTaskStatus = (task: Task) => {
    const newStatus = task.status === "completed" ? "todo" : "completed"
    updateTaskStatusMutation.mutate({ id: task.id, status: newStatus })
  }

  // Handle task deletion
  const handleDeleteTask = (taskId: string) => {
    deleteTaskMutation.mutate(taskId)
    onDeleteTask(taskId)
  }

  // Priority colors and icons
  const getPriorityConfig = (priority: string) => {
    switch (priority) {
      case "urgent":
        return { color: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300", icon: AlertCircle }
      case "high":
        return { color: "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300", icon: Flag }
      case "medium":
        return { color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300", icon: Flag }
      case "low":
        return { color: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300", icon: Flag }
      default:
        return { color: "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300", icon: Flag }
    }
  }

  // Status colors and icons
  const getStatusConfig = (status: string) => {
    switch (status) {
      case "completed":
        return { color: "text-green-600 dark:text-green-400", icon: CheckCircle2 }
      case "in_progress":
        return { color: "text-blue-600 dark:text-blue-400", icon: Clock }
      case "cancelled":
        return { color: "text-red-600 dark:text-red-400", icon: Circle }
      default:
        return { color: "text-gray-600 dark:text-gray-400", icon: Circle }
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-teal-600"></div>
          <span>Loading tasks...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 dark:text-red-400">Failed to load tasks</p>
          <p className="text-sm text-gray-500 mt-1">
            {error instanceof Error ? error.message : 'Please try refreshing the page'}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
          >
            Refresh Page
          </button>
        </div>
      </div>
    )
  }

  // Additional safety check for data integrity
  if (!isLoading && !error && (!tasksArray || tasksArray.length === 0)) {
    console.log('Tasks debug info:', { tasksResponse, tasksArray, isLoading, error })
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search tasks..."
            value={localSearch}
            onChange={(e) => setLocalSearch(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-[150px]">
            <Filter className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="todo">To Do</SelectItem>
            <SelectItem value="in_progress">In Progress</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="cancelled">Cancelled</SelectItem>
          </SelectContent>
        </Select>
        <Select value={priorityFilter} onValueChange={setPriorityFilter}>
          <SelectTrigger className="w-full sm:w-[150px]">
            <Flag className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Priority" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Priority</SelectItem>
            <SelectItem value="urgent">Urgent</SelectItem>
            <SelectItem value="high">High</SelectItem>
            <SelectItem value="medium">Medium</SelectItem>
            <SelectItem value="low">Low</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Task List */}
      <div className="space-y-2">
        {filteredTasks.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            {tasksArray.length === 0 ? (
              <div>
                <p className="text-lg font-medium mb-2">No tasks available</p>
                <p className="text-sm">Create a new task to get started with your project management.</p>
                {!isLoading && !error && (
                  <p className="text-xs mt-2 text-gray-400">
                    Debug: Response structure may be unexpected. Check console for details.
                  </p>
                )}
              </div>
            ) : (
              <div>
                <p className="text-lg font-medium mb-2">No tasks match your filters</p>
                <p className="text-sm">Try adjusting your search terms or filter criteria.</p>
                <p className="text-xs mt-2">
                  Showing 0 of {tasksArray.length} total tasks
                </p>
              </div>
            )}
          </div>
        ) : (
          filteredTasks.map((task: Task) => {
            const priorityConfig = getPriorityConfig(task.priority)
            const statusConfig = getStatusConfig(task.status)
            const StatusIcon = statusConfig.icon
            const canModify = isAdmin || task.assigned_to === currentUserId

            return (
              <Card key={task.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    {/* Status Checkbox */}
                    <div className="flex items-center pt-1">
                      <Checkbox
                        checked={task.status === "completed"}
                        onCheckedChange={() => handleToggleTaskStatus(task)}
                        disabled={updateTaskStatusMutation.isPending}
                        className="h-5 w-5"
                      />
                    </div>

                    {/* Task Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-2">
                        <div className="flex-1">
                          <h3 className={`font-medium ${task.status === "completed" ? "line-through text-gray-500" : ""}`}>
                            {task.title}
                          </h3>
                          {task.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                              {task.description}
                            </p>
                          )}
                        </div>
                        
                        {/* Priority Badge */}
                        <Badge className={`text-xs ${priorityConfig.color}`}>
                          {task.priority}
                        </Badge>
                      </div>

                      {/* Task Meta Information */}
                      <div className="flex items-center gap-4 mt-3 text-xs text-gray-500 dark:text-gray-400">
                        {task.assigned_to_name && (
                          <div className="flex items-center gap-1">
                            <User className="h-3 w-3" />
                            <span>{task.assigned_to_name}</span>
                          </div>
                        )}
                        {task.due_date && (
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            <span>{format(new Date(task.due_date), "MMM d, yyyy")}</span>
                          </div>
                        )}
                        <div className="flex items-center gap-1">
                          <StatusIcon className={`h-3 w-3 ${statusConfig.color}`} />
                          <span className="capitalize">{task.status.replace("_", " ")}</span>
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    {canModify && (
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 text-gray-500 hover:text-blue-600"
                          onClick={() => onEditTask(task)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 text-gray-500 hover:text-red-600"
                          onClick={() => handleDeleteTask(task.id)}
                          disabled={deleteTaskMutation.isPending}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )
          })
        )}
      </div>
    </div>
  )
}
